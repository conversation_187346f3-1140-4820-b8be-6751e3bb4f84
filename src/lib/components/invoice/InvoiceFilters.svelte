<script lang="ts">
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';
	import {
		formatAccountingPeriod,
		getYearsFromPeriods,
		getPeriodsForYear,
	} from '$lib/schemas/invoice';
	import type { InvoiceListItem } from '$lib/schemas/invoice';

	const {
		invoices,
		onFilterChange,
	}: {
		invoices: InvoiceListItem[];
		onFilterChange: (filters: { period?: string; year?: number }) => void;
	} = $props();

	let selectedPeriod = $state<string | undefined>(undefined);
	let selectedYear = $state<number | undefined>(undefined);

	// Get unique years from existing invoice periods
	const availableYears = $derived(() => {
		const periods = invoices.map((invoice) => invoice.period);
		return getYearsFromPeriods(periods);
	});

	// Get periods for selected year
	const periodsForYear = $derived(() => {
		if (selectedYear) {
			return getPeriodsForYear(selectedYear);
		}
		// Default to current year if no year selected
		const currentYear = new Date().getFullYear();
		return getPeriodsForYear(currentYear);
	});

	// Handle filter changes
	function handlePeriodChange(period: string | undefined) {
		selectedPeriod = period;
		onFilterChange({ period, year: selectedYear });
	}

	function handleYearChange(year: number | undefined) {
		selectedYear = year;
		// Reset period when year changes
		selectedPeriod = undefined;
		onFilterChange({ period: undefined, year });
	}

	function clearFilters() {
		selectedPeriod = undefined;
		selectedYear = undefined;
		onFilterChange({});
	}
</script>

<div class="bg-muted/50 flex flex-wrap items-center gap-4 rounded-lg p-4">
	<div class="flex items-center gap-2">
		<span class="text-sm font-medium">Filter by:</span>
	</div>

	<!-- Year Filter -->
	<div class="min-w-[150px]">
		<Select.Root
			type="single"
			value={selectedYear?.toString()}
			onValueChange={(value) => {
				handleYearChange(value ? parseInt(value, 10) : undefined);
			}}
		>
			<Select.Trigger>
				{selectedYear ? selectedYear.toString() : 'All Years'}
			</Select.Trigger>
			<Select.Content>
				<Select.Item value="">All Years</Select.Item>
				{#each availableYears() as year (year)}
					<Select.Item value={year.toString()}>
						{year}
					</Select.Item>
				{/each}
			</Select.Content>
		</Select.Root>
	</div>

	<!-- Period Filter -->
	<div class="min-w-[200px]">
		<Select.Root type="single" value={selectedPeriod} onValueChange={handlePeriodChange}>
			<Select.Trigger>
				{selectedPeriod ? formatAccountingPeriod(selectedPeriod) : 'All Periods'}
			</Select.Trigger>
			<Select.Content>
				<Select.Item value="">All Periods</Select.Item>
				{#each periodsForYear() as period (period)}
					<Select.Item value={period}>
						{formatAccountingPeriod(period)}
					</Select.Item>
				{/each}
			</Select.Content>
		</Select.Root>
	</div>

	<!-- Clear Filters Button -->
	{#if selectedPeriod || selectedYear}
		<Button variant="outline" size="sm" onclick={clearFilters}>Clear Filters</Button>
	{/if}

	<!-- Results Count -->
	<div class="text-muted-foreground ml-auto text-sm">
		{invoices.length} invoice{invoices.length === 1 ? '' : 's'}
	</div>
</div>
