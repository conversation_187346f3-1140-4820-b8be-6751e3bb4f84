<script lang="ts">
	import type { PageProps } from './$types';
	import * as Form from '$lib/components/ui/form';
	import * as Select from '$lib/components/ui/select';
	import * as Calendar from '$lib/components/ui/calendar';
	import * as Popover from '$lib/components/ui/popover';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { editPurchaseOrderSchema } from '$lib/schemas/purchase_order';
	import { toast } from 'svelte-sonner';
	import CalendarIcon from 'phosphor-svelte/lib/Calendar';
	import { DateFormatter, getLocalTimeZone, parseDate } from '@internationalized/date';
	import type { DateValue } from '@internationalized/date';
	import { invalidate } from '$app/navigation';

	const { data }: PageProps = $props();
	const { vendors, workPackages, purchaseOrder } = data;

	const form = superForm(data.form, {
		validators: zodClient(editPurchaseOrderSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
			invalidate('project:purchase-orders');
		},
	});

	const { form: formData, enhance } = form;

	// Date handling
	const df = new DateFormatter('en-GB', {
		dateStyle: 'long',
	});

	let selectedVendor = $state($formData.vendor_id);
	let selectedWorkPackage = $state($formData.work_package_id || '');
	let calendarOpen = $state(false);
	let selectedDate = $state<DateValue | undefined>(
		$formData.po_date ? parseDate($formData.po_date) : undefined,
	);

	// Update form data when vendor selection changes
	$effect(() => {
		$formData.vendor_id = selectedVendor;
	});

	// Update form data when work package selection changes
	$effect(() => {
		$formData.work_package_id = selectedWorkPackage || '';
	});

	// Update form data when date changes
	$effect(() => {
		if (selectedDate) {
			$formData.po_date = selectedDate.toString();
		}
	});
</script>

<div class="container mx-auto py-8">
	<div class="mb-6">
		<h1 class="text-2xl font-semibold">Edit Purchase Order</h1>
		<p class="text-muted-foreground mt-1">
			Edit purchase order {purchaseOrder.po_number}
		</p>
	</div>

	<div class="max-w-2xl">
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Basic Information -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Basic Information</h2>
					<input type="hidden" name="purchase_order_id" bind:value={$formData.purchase_order_id} />

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="po_number">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>PO Number <span class="text-red-500">*</span></Form.Label>
									<Input {...props} bind:value={$formData.po_number} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="po_date">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>PO Date <span class="text-red-500">*</span></Form.Label>
									<Popover.Root bind:open={calendarOpen}>
										<Popover.Trigger>
											{#snippet child({ props: triggerProps })}
												<Button
													{...props}
													{...triggerProps}
													variant="outline"
													class="w-full justify-start text-left font-normal"
												>
													<CalendarIcon class="mr-2 h-4 w-4" />
													{selectedDate
														? df.format(selectedDate.toDate(getLocalTimeZone()))
														: 'Pick a date'}
												</Button>
											{/snippet}
										</Popover.Trigger>
										<Popover.Content class="w-auto p-0" align="start">
											<Calendar.Calendar
												type="single"
												bind:value={selectedDate}
												onValueChange={(date: DateValue | undefined) => {
													selectedDate = date;
													calendarOpen = false;
												}}
												initialFocus
											/>
										</Popover.Content>
									</Popover.Root>
									<input type="hidden" name="po_date" bind:value={$formData.po_date} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<Form.Field {form} name="vendor_id">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Vendor <span class="text-red-500">*</span></Form.Label>
								<Select.Root type="single" bind:value={selectedVendor} name={props.name}>
									<Select.Trigger {...props}>
										{selectedVendor
											? vendors.find((v) => v.vendor_id === selectedVendor)?.name
											: 'Select vendor'}
									</Select.Trigger>
									<Select.Content>
										{#each vendors as vendor (vendor.vendor_id)}
											<Select.Item value={vendor.vendor_id}>
												{vendor.name}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<!-- Work Package Selection -->
					{#if workPackages && workPackages.length > 0}
						<Form.Field {form} name="work_package_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Work Package <span class="text-red-500">*</span></Form.Label>
									<Select.Root type="single" bind:value={selectedWorkPackage} name={props.name}>
										<Select.Trigger {...props}>
											{selectedWorkPackage
												? workPackages.find((wp) => wp.work_package_id === selectedWorkPackage)
														?.name
												: 'Select work package'}
										</Select.Trigger>
										<Select.Content>
											{#each workPackages as workPackage (workPackage.work_package_id)}
												<Select.Item value={workPackage.work_package_id}>
													{workPackage.name}
													{#if workPackage.wbs_code}
														- {workPackage.wbs_code}
													{/if}
												</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					{:else}
						<div class="text-sm text-red-500">
							No work packages available. Please create a work package before creating a purchase
							order.
						</div>
					{/if}

					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description</Form.Label>
								<Textarea {...props} bind:value={$formData.description} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="account">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Account</Form.Label>
								<Input {...props} bind:value={$formData.account} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Financial Information -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Financial Information</h2>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="original_amount">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Original Amount</Form.Label>
									<Input
										{...props}
										type="number"
										step="0.01"
										bind:value={$formData.original_amount}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="co_amount">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Change Order Amount</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.co_amount} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="freight">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Freight</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.freight} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="tax">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Tax</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.tax} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="other">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Other</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.other} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>
				</div>

				<!-- Notes -->
				<Form.Field {form} name="notes">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Notes</Form.Label>
							<Textarea {...props} bind:value={$formData.notes} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Actions -->
				<div class="flex gap-4">
					<Button type="submit">Update Purchase Order</Button>
					<Button type="button" variant="outline" onclick={() => history.back()}>Cancel</Button>
				</div>
			</div>
		</form>
	</div>
</div>
