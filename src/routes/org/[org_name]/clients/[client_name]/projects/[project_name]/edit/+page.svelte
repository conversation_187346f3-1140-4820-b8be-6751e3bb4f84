<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import { superForm, intProxy } from 'sveltekit-superforms';
	import type { PageProps } from './$types';
	import { toast } from 'svelte-sonner';

	const { data }: PageProps = $props();

	const form = superForm(data.form, {
		resetForm: false,
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form: formData, enhance } = form;

	// Create integer proxy for WBS library ID to handle proper type conversion
	const wbsLibraryProxy = intProxy(formData, 'wbs_library_id');
</script>

<div class="container mx-auto max-w-2xl py-8">
	<h1>Edit Project</h1>

	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Project Name -->
				<Form.Field {form} name="name">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Project Name <span class="text-red-500">*</span></Form.Label>
							<Input
								{...props}
								bind:value={$formData.name}
								required
								placeholder="Enter project name"
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Description -->
				<Form.Field {form} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description</Form.Label>
							<Textarea
								{...props}
								placeholder="Brief description of the project"
								class="resize-none"
								bind:value={$formData.description}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- WBS Library Selection -->
				<Form.Field {form} name="wbs_library_id">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>WBS Library</Form.Label>
							<Select.Root type="single" bind:value={$wbsLibraryProxy} name={props.name}>
								<Select.Trigger class="w-full" {...props}>
									{$formData.wbs_library_id
										? data.wbsLibraries.find(
												(lib) => lib.wbs_library_id === $formData.wbs_library_id,
											)?.name
										: 'Select a WBS library'}
								</Select.Trigger>
								<Select.Content>
									{#each data.wbsLibraries as library (library.wbs_library_id)}
										<Select.Item value={String(library.wbs_library_id)} label={library.name} />
									{/each}
								</Select.Content>
							</Select.Root>
						{/snippet}
					</Form.Control>
					<Form.Description
						>Select a work breakdown structure library to use with this project</Form.Description
					>
					<Form.FieldErrors />
				</Form.Field>

				<div class="pt-4">
					<Form.Button class="w-full">Update Project</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
