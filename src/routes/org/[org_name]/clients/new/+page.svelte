<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form';
	import { Textarea } from '$lib/components/ui/textarea';
	import { superForm } from 'sveltekit-superforms';
	import type { PageProps } from './$types';
	import Label from '$lib/components/ui/label/label.svelte';
	import { toast } from 'svelte-sonner';
	import { invalidate } from '$app/navigation';

	const { data }: PageProps = $props();

	const formHandler = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
			invalidate('sidebar:clients');
		},
	});
	const { form, enhance } = formHandler;

	// File upload handling
	// let logoFiles: FileList | undefined = $state(undefined);
	// let logoPreview: string | null = $state(null);

	// $effect(() => {
	// 	if (logoFiles && logoFiles.length > 0) {
	// 		const reader = new FileReader();
	// 		reader.onload = (e) => {
	// 			logoPreview = e.target?.result as string;
	// 		};
	// 		reader.readAsDataURL(logoFiles[0]);
	// 	}
	// });
</script>

<div class="container mx-auto max-w-2xl py-8">
	<h1>Create a Client</h1>

	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance enctype="multipart/form-data">
			<div class="space-y-6">
				<!-- Client Name -->
				<Form.Field form={formHandler} name="name">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Client Name <span class="text-red-500">*</span></Form.Label>
							<Input {...props} bind:value={$form.name} required placeholder="Enter client name" />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<!-- Website URL -->

				<Form.Field form={formHandler} name="client_url">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Website URL</Form.Label>
							<Input
								{...props}
								type="url"
								placeholder="https://example.com"
								bind:value={$form.client_url}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Internal URL -->
				<Form.Field form={formHandler} name="internal_url">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Link to Teams Site</Form.Label>
							<Input
								{...props}
								type="url"
								placeholder="https://teams.microsoft.com/..."
								bind:value={$form.internal_url}
							/>
						{/snippet}
					</Form.Control>
					<Form.Description>URL to Teams or other internal client document portal</Form.Description>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Internal URL Description -->
				<Form.Field form={formHandler} name="internal_url_description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Internal URL Description</Form.Label>
							<Input
								{...props}
								placeholder="Teams Site"
								bind:value={$form.internal_url_description}
							/>
						{/snippet}
					</Form.Control>
					<Form.Description>A short description of what the internal URL links to</Form.Description>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Description -->
				<Form.Field form={formHandler} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description</Form.Label>
							<Textarea
								{...props}
								placeholder="Brief description of the client"
								class="resize-none"
								bind:value={$form.description}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Logo Upload -->
				<div class="@container space-y-2">
					<Label for="logo">Logo by URL</Label>
					<!-- <Label for="logo">Logo by URL or File</Label> -->
					<div class="flex flex-col gap-4 @lg:flex-row">
						<div class="flex-1">
							<Input
								type="url"
								id="logo_url"
								name="logo_url"
								placeholder="https://example.com/logo.png"
							/>
						</div>
						<!-- <div class="text-center">
							<span class="text-muted-foreground">or</span>
						</div>
						<div class="flex-1">
							<Input
								type="file"
								id="logo_file"
								name="logo_file"
								accept="image/*"
								bind:files={logoFiles}
							/>
						</div>
					</div>
					{#if logoPreview}
						<div class="mt-4">
							<p class="mb-2 text-sm text-muted-foreground">Preview:</p>
							<img
								src={logoPreview}
								alt="Logo preview"
								class="max-h-32 max-w-full rounded border"
							/>
						</div>
					{/if} -->
					</div>

					<div class="pt-4">
						<Form.Button class="w-full">Submit</Form.Button>
					</div>
				</div>
			</div>
		</form>
	</div>
</div>
