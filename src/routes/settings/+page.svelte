<script lang="ts">
	import type { PageProps } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { toast } from 'svelte-sonner';

	const { data }: PageProps = $props();
	const formHandler = superForm(data.form, {
		resetForm: false,
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form, enhance } = formHandler;
</script>

<div class="container mx-auto max-w-md py-16">
	<h1 class="mb-6 text-2xl font-semibold">Profile Settings</h1>
	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-4">
				<Form.Field form={formHandler} name="email">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email <span class="text-red-500">*</span></Form.Label>
							<Input {...props} bind:value={$form.email} placeholder="Your email" />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={formHandler} name="full_name">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Full Name <span class="text-red-500">*</span></Form.Label>
							<Input {...props} bind:value={$form.full_name} placeholder="Your full name" />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={formHandler} name="avatar_url">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Avatar URL</Form.Label>
							<Input
								{...props}
								bind:value={$form.avatar_url}
								type="url"
								placeholder="https://example.com/avatar.png"
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="pt-6">
					<Form.Button class="w-full">Save Profile</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
