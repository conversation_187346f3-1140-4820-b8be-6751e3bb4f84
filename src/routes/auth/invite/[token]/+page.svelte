<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { superForm } from 'sveltekit-superforms';
	import type { PageProps } from './$types';
	import { toast } from 'svelte-sonner';

	const { data }: PageProps = $props();
	const { token, name, role, resource_type } = data;

	const acceptFormHandler = superForm(data.acceptForm, {
		id: 'accept-invitation',
		onUpdated: ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const declineFormHandler = superForm(data.declineForm, {
		id: 'decline-invitation',
		onUpdated: ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { enhance: enhanceAccept } = acceptFormHandler;
	const { enhance: enhanceDecline } = declineFormHandler;
</script>

<div class="container mx-auto max-w-lg py-8">
	<div class="rounded-lg border p-8 text-center">
		<h1>Client Invitation</h1>
		<p class="mb-6">
			{#if data.inviter}
				<span class="font-semibold">{data.inviter}</span> has invited you to join the
				<span class="font-semibold">{name}</span>
				{resource_type} as a
				<span class="font-semibold capitalize">{role}</span>.
			{:else}
				You've been invited to join the <span class="font-semibold">{name}</span>
				{resource_type} as a
				<span class="font-semibold capitalize">{role}</span>.
			{/if}
		</p>

		<div class="flex justify-center gap-4">
			<form method="POST" action="?/accept" use:enhanceAccept>
				<input type="hidden" name="token" value={token} />
				<Button type="submit" variant="default">Accept Invitation</Button>
			</form>

			<form method="POST" action="?/decline" use:enhanceDecline>
				<input type="hidden" name="token" value={token} />
				<Button type="submit" variant="outline">Decline Invitation</Button>
			</form>
		</div>
	</div>
</div>
